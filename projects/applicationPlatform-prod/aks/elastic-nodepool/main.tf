############ MODULES ############
module "nodepool" {
  source            = "./../../../../modules/nodepool"
  resource_group    = var.resource_group
  prefix            = var.prefix
  virtual_network   = var.virtual_network
  subnet_name       = var.subnet_name
  subnet_prefix     = var.subnet_prefix
  node_pool_prefix  = var.node_pool_prefix
  node_pool_taints  = var.node_pool_taints
  node_pool_vm_size = var.node_pool_vm_size
  node_pool_count   = var.node_pool_count
}

resource "null_resource" "prevent_destroy" {
  count = 1

  depends_on = [module.nodepool]

  triggers = {
    vm_id = module.nodepool.cluster_node_pool_id
  }

  lifecycle {
    prevent_destroy = true
  }
}

terraform {
  backend "azurerm" {
    resource_group_name  = "AGW_PROD-AP"
    storage_account_name = "tfazaksprod"
    container_name       = "application-platform"
    key                  = "k8s-ap-prod-elasticpool1.tfstate"
  }
}
