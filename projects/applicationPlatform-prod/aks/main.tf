############ MODULES ############
module "cluster" {
  source                 = "./../../../modules/cluster"
  prefix                 = var.prefix
  subnet_prefix          = var.subnet_prefix
  network_plugin         = var.network_plugin
  network_plugin_mode    = var.network_plugin_mode
  node_pool_vm_size      = var.node_pool_vm_size
  system_node_pool_count = var.system_node_pool_count
}

resource "null_resource" "prevent_destroy" {
  count = 1

  depends_on = [module.cluster]

  triggers = {
    vm_id = module.cluster.kubernetes_cluster_id
  }

  lifecycle {
    prevent_destroy = true
  }
}

terraform {
  backend "azurerm" {
    resource_group_name  = "AGW_PROD-AP"
    storage_account_name = "tfazaksprod"
    container_name       = "application-platform"
    key                  = "k8s-ap-prod.tfstate"
  }
}
