############ MODULES ############
module "storage_account" {
  source                          = "./../../../../modules/storage_account"
  resource_group_name             = var.resource_group_name
  storage_account_name            = var.storage_account_name
  account_kind                    = var.account_kind
  account_replication_type        = var.account_replication_type
  account_tier                    = var.account_tier
  allow_nested_items_to_be_public = var.allow_nested_items_to_be_public
  private_endpoint_creation       = var.private_endpoint_creation
}

terraform {
  backend "azurerm" {
    resource_group_name  = "AGW_PROD-AP"
    storage_account_name = "tfazbaseprod"
    container_name       = "storage-account"
    key                  = "k8sharborpremium.tfstate"
  }
}
