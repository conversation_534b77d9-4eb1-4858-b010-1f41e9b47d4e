############ MODULES ############
module "storage_account" {
  source                        = "./../../../../modules/storage_account"
  resource_group_name           = var.resource_group_name
  storage_account_name          = var.storage_account_name
  container_names               = var.container_names
}

terraform {
  backend "azurerm" {
    resource_group_name         = "AGW_PROD-AP"
    storage_account_name        = "tfazbaseprod"
    container_name              = "storage-account"
    key                         = "k8selasticprod.tfstate"
  }
}