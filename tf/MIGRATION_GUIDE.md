# Migration Guide: From Single Configuration to Environment-Based Structure

This guide helps you migrate from the old single-file Terraform configuration to the new environment-based structure.

## What Changed

### Before (Old Structure)
```
tf/
├── main.tf          # All environments in one file
├── locals.tf        # All configuration in locals
├── providers.tf     # Multiple provider aliases
└── modules/
    └── vault-environment/
```

### After (New Structure)
```
tf/
├── environments/
│   ├── development/
│   ├── staging/
│   └── production/
├── modules/
│   └── vault-environment/
└── shared/
    └── services.tf
```

## Migration Steps

### 1. Backup Existing State
```bash
# Backup your existing state files
cp terraform.tfstate terraform.tfstate.backup
cp terraform.tfstate.backup terraform.tfstate.old
```

### 2. Initialize New Environment Configurations
```bash
# Initialize each environment separately
cd tf/environments/development
terraform init

cd ../staging
terraform init

cd ../production
terraform init
```

### 3. Import Existing Resources (if needed)
If you have existing resources, you may need to import them into the new state files:

```bash
# Example: Import existing vault policies
cd tf/environments/development
terraform import vault_policy.service_policies["account"] account-development
terraform import vault_policy.service_policies["gateway"] gateway-development
terraform import vault_policy.service_policies["payment"] payment-development
```

### 4. Verify Configuration
```bash
# Plan each environment to verify configuration
cd tf/environments/development
terraform plan

cd ../staging
terraform plan

cd ../production
terraform plan
```

## Key Differences

### Variable Management
**Before**: Variables were defined in `locals.tf`
```hcl
locals {
  environments = {
    development = { vault_address = "...", ... }
  }
}
```

**After**: Variables are defined per environment in `terraform.tfvars`
```hcl
environment_config = {
  vault_address = "http://localhost:8201"
  vault_token   = "f23612cf-824d-4206-9e94-e31a6dc8ee8d"
  # ...
}
```

### Provider Configuration
**Before**: Multiple provider aliases in one file
```hcl
provider "vault" {
  alias = "development"
  # ...
}
provider "vault" {
  alias = "staging"
  # ...
}
```

**After**: Single provider per environment
```hcl
provider "vault" {
  address = var.environment_config.vault_address
  token   = var.environment_config.vault_token
}
```

### State Management
**Before**: Single state file for all environments
- Risk of affecting multiple environments
- Difficult to manage permissions

**After**: Separate state file per environment
- Independent deployments
- Better security isolation
- Easier rollbacks

## Rollback Plan

If you need to rollback to the old structure:

1. The old files are backed up with `.old` extension
2. Restore the old files:
   ```bash
   cd tf/
   mv main.tf.old main.tf
   mv locals.tf.old locals.tf
   mv providers.tf.old providers.tf
   ```
3. Remove the new environment directories:
   ```bash
   rm -rf environments/
   ```

## Troubleshooting

### State File Issues
If you encounter state file conflicts:
```bash
# Reset state and re-import resources
terraform state list
terraform state rm <resource_name>
terraform import <resource_type>.<resource_name> <resource_id>
```

### Provider Configuration Issues
Ensure each environment has the correct provider configuration:
```bash
# Verify provider configuration
terraform providers
terraform validate
```

### Module Path Issues
The module path has changed from `./modules/vault-environment` to `../../modules/vault-environment`:
- Update any hardcoded paths in your configuration
- Verify module source paths are correct

## Benefits After Migration

1. **Independent Deployments**: Deploy environments separately
2. **Better Security**: Environment-specific access controls
3. **Reduced Risk**: Changes to one environment don't affect others
4. **Easier CI/CD**: Environment-specific pipelines
5. **Better Organization**: Clear separation of concerns

## Support

If you encounter issues during migration:
1. Check the backup files (`.old` extensions)
2. Verify module paths are correct
3. Ensure provider configurations match your environment
4. Review the environment-specific README files

The new structure follows Terraform best practices and will make your infrastructure more maintainable and secure.
