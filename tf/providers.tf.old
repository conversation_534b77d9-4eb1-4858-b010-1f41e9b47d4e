# Dynamic vault providers for each environment
provider "vault" {
  alias   = "development"
  address = local.environments.development.vault_address
  token   = local.environments.development.vault_token
}

provider "vault" {
  alias   = "staging"
  address = local.environments.staging.vault_address
  token   = local.environments.staging.vault_token
}

provider "vault" {
  alias   = "production"
  address = local.environments.production.vault_address
  token   = local.environments.production.vault_token
}
