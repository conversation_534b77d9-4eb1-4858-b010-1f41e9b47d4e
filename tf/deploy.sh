#!/bin/bash

# Form3 Platform Terraform Deployment Script
# Usage: ./deploy.sh [environment] [action]
# Example: ./deploy.sh development plan
# Example: ./deploy.sh production apply

set -e

ENVIRONMENT=$1
ACTION=${2:-plan}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Validate inputs
if [ -z "$ENVIRONMENT" ]; then
    echo "Usage: $0 <environment> [action]"
    echo "Environments: development, staging, production"
    echo "Actions: init, plan, apply, destroy"
    exit 1
fi

if [ ! -d "$SCRIPT_DIR/environments/$ENVIRONMENT" ]; then
    echo "Error: Environment '$ENVIRONMENT' not found"
    echo "Available environments:"
    ls -1 "$SCRIPT_DIR/environments/"
    exit 1
fi

# Change to environment directory
cd "$SCRIPT_DIR/environments/$ENVIRONMENT"

echo "=========================================="
echo "Form3 Platform Terraform Deployment"
echo "Environment: $ENVIRONMENT"
echo "Action: $ACTION"
echo "Directory: $(pwd)"
echo "=========================================="

# Execute the requested action
case $ACTION in
    init)
        echo "Initializing Terraform..."
        terraform init
        ;;
    plan)
        echo "Planning Terraform changes..."
        terraform plan
        ;;
    apply)
        echo "Applying Terraform changes..."
        terraform plan -out=tfplan
        echo "Review the plan above. Press Enter to continue or Ctrl+C to cancel..."
        read -r
        terraform apply tfplan
        rm -f tfplan
        ;;
    destroy)
        echo "WARNING: This will destroy all resources in $ENVIRONMENT!"
        echo "Type 'yes' to confirm:"
        read -r confirmation
        if [ "$confirmation" = "yes" ]; then
            terraform destroy
        else
            echo "Destroy cancelled."
        fi
        ;;
    validate)
        echo "Validating Terraform configuration..."
        terraform validate
        terraform fmt -check
        ;;
    *)
        echo "Error: Unknown action '$ACTION'"
        echo "Available actions: init, plan, apply, destroy, validate"
        exit 1
        ;;
esac

echo "=========================================="
echo "Action '$ACTION' completed for environment '$ENVIRONMENT'"
echo "=========================================="
