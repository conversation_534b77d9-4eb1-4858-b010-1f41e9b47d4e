# Environment-Specific Terraform Configurations

This directory contains separate Terraform configurations for each environment, following best practices for infrastructure as code.

## Directory Structure

```
tf/environments/
├── development/
│   ├── main.tf           # Main module call
│   ├── variables.tf      # Variable definitions
│   ├── terraform.tfvars  # Environment-specific values
│   └── providers.tf      # Provider configuration
├── staging/
│   ├── main.tf
│   ├── variables.tf
│   ├── terraform.tfvars
│   └── providers.tf
├── production/
│   ├── main.tf
│   ├── variables.tf
│   ├── terraform.tfvars
│   └── providers.tf
└── README.md            # This file
```

## Benefits of This Structure

1. **Independent Deployments**: Each environment can be deployed separately
2. **Separate State Files**: No risk of accidentally affecting other environments
3. **Environment-Specific Configuration**: Each environment has its own variables and settings
4. **Better Security**: Production can have different access controls than development
5. **Reduced Blast Radius**: Changes to one environment don't affect others

## Usage

### Deploy Development Environment
```bash
cd tf/environments/development
terraform init
terraform plan
terraform apply
```

### Deploy Staging Environment
```bash
cd tf/environments/staging
terraform init
terraform plan
terraform apply
```

### Deploy Production Environment
```bash
cd tf/environments/production
terraform init
terraform plan
terraform apply
```

## Configuration

Each environment has its own `terraform.tfvars` file with environment-specific values:

- **Vault addresses and tokens**
- **Network names**
- **Port configurations**
- **Service configurations**

## Adding New Environments

1. Create a new directory under `tf/environments/`
2. Copy the files from an existing environment
3. Update the `terraform.tfvars` file with new environment values
4. Update the backend configuration in `main.tf` if using remote state

## CI/CD Integration

Each environment should have its own CI/CD pipeline:

```yaml
# Example GitLab CI structure
stages:
  - validate
  - plan-dev
  - apply-dev
  - plan-staging
  - apply-staging
  - plan-prod
  - apply-prod

validate:
  script:
    - terraform fmt -check -recursive
    - terraform validate

plan-dev:
  script:
    - cd tf/environments/development
    - terraform plan -out=plan.tfplan

apply-dev:
  script:
    - cd tf/environments/development
    - terraform apply plan.tfplan
  only:
    - develop

plan-staging:
  script:
    - cd tf/environments/staging
    - terraform plan -out=plan.tfplan

apply-staging:
  script:
    - cd tf/environments/staging
    - terraform apply plan.tfplan
  only:
    - main

plan-prod:
  script:
    - cd tf/environments/production
    - terraform plan -out=plan.tfplan

apply-prod:
  script:
    - cd tf/environments/production
    - terraform apply plan.tfplan
  when: manual
  only:
    - main
```

This structure provides better control, security, and maintainability for your infrastructure deployments.
