environment_name = "production"

environment_config = {
  vault_address = "http://localhost:8301"
  vault_token   = "083672fc-4471-4ec4-9b59-a285e463a973"
  network_name  = "vagrant_production"
  frontend_port = 4081
  vault_host    = "vault-production"
}

services = {
  account = {
    image         = "form3tech-oss/platformtest-account"
    vault_enabled = true
  }
  gateway = {
    image         = "form3tech-oss/platformtest-gateway"
    vault_enabled = true
  }
  payment = {
    image         = "form3tech-oss/platformtest-payment"
    vault_enabled = true
  }
  frontend = {
    image            = "docker.io/nginx:latest"
    vault_enabled    = false
    production_image = "docker.io/nginx:1.22.0-alpine"
  }
}
