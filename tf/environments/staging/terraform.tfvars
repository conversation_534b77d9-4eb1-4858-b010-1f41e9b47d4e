environment_name = "staging"

environment_config = {
  vault_address = "http://localhost:8202"
  vault_token   = "staging-vault-token-12345"
  network_name  = "vagrant_staging"
  frontend_port = 4082
  vault_host    = "vault-staging"
}

services = {
  account = {
    image         = "form3tech-oss/platformtest-account"
    vault_enabled = true
  }
  gateway = {
    image         = "form3tech-oss/platformtest-gateway"
    vault_enabled = true
  }
  payment = {
    image         = "form3tech-oss/platformtest-payment"
    vault_enabled = true
  }
  frontend = {
    image            = "docker.io/nginx:latest"
    vault_enabled    = false
    production_image = "docker.io/nginx:1.22.0-alpine"
  }
}
