#!/bin/bash

# Form3 Platform Terraform Verification Script
# This script validates the new environment-based structure

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENVIRONMENTS=("development" "staging" "production")
ERRORS=0

echo "=========================================="
echo "Form3 Platform Terraform Structure Verification"
echo "=========================================="

# Check if environment directories exist
echo "Checking environment directories..."
for env in "${ENVIRONMENTS[@]}"; do
    if [ -d "$SCRIPT_DIR/environments/$env" ]; then
        echo "✓ Environment directory exists: $env"
    else
        echo "✗ Environment directory missing: $env"
        ((ERRORS++))
    fi
done

# Check required files in each environment
echo ""
echo "Checking required files in each environment..."
REQUIRED_FILES=("main.tf" "variables.tf" "terraform.tfvars" "providers.tf")

for env in "${ENVIRONMENTS[@]}"; do
    echo "Checking $env environment..."
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$SCRIPT_DIR/environments/$env/$file" ]; then
            echo "  ✓ $file exists"
        else
            echo "  ✗ $file missing"
            ((ERRORS++))
        fi
    done
done

# Check module directory
echo ""
echo "Checking module structure..."
if [ -d "$SCRIPT_DIR/modules/vault-environment" ]; then
    echo "✓ vault-environment module directory exists"
else
    echo "✗ vault-environment module directory missing"
    ((ERRORS++))
fi

# Validate Terraform syntax for each environment
echo ""
echo "Validating Terraform syntax..."
for env in "${ENVIRONMENTS[@]}"; do
    if [ -d "$SCRIPT_DIR/environments/$env" ]; then
        echo "Validating $env environment..."
        cd "$SCRIPT_DIR/environments/$env"
        
        if terraform validate > /dev/null 2>&1; then
            echo "  ✓ Terraform syntax valid"
        else
            echo "  ✗ Terraform syntax invalid"
            terraform validate
            ((ERRORS++))
        fi
        
        if terraform fmt -check > /dev/null 2>&1; then
            echo "  ✓ Terraform formatting correct"
        else
            echo "  ⚠ Terraform formatting needs adjustment (run 'terraform fmt')"
        fi
    fi
done

# Check for old files (should be backed up)
echo ""
echo "Checking for old files..."
OLD_FILES=("main.tf.old" "locals.tf.old" "providers.tf.old")
for file in "${OLD_FILES[@]}"; do
    if [ -f "$SCRIPT_DIR/$file" ]; then
        echo "✓ Backup file exists: $file"
    else
        echo "⚠ Backup file missing: $file"
    fi
done

# Summary
echo ""
echo "=========================================="
if [ $ERRORS -eq 0 ]; then
    echo "✓ All checks passed! The new structure is ready to use."
    echo ""
    echo "Next steps:"
    echo "1. Test deployment: ./deploy.sh development plan"
    echo "2. Deploy development: ./deploy.sh development apply"
    echo "3. Deploy staging: ./deploy.sh staging apply"
    echo "4. Deploy production: ./deploy.sh production apply"
else
    echo "✗ $ERRORS error(s) found. Please fix the issues above."
    exit 1
fi
echo "=========================================="
