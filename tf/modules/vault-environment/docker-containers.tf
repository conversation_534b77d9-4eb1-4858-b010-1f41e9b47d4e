# Docker containers for vault-enabled services in this environment
resource "docker_container" "vault_services" {
  for_each = var.vault_enabled_services

  image = each.value.image
  name  = "${each.key}_${var.environment_name}"

  env = [
    "VAULT_ADDR=http://${var.environment_config.vault_host}:8200",
    "VAULT_USERNAME=${each.key}-${var.environment_name}",
    "VAULT_PASSWORD=123-${each.key}-${var.environment_name}",
    "ENVIRONMENT=${var.environment_name}"
  ]

  networks_advanced {
    name = var.environment_config.network_name
  }

  # Ensure container stays running
  must_run = true
  restart  = "unless-stopped"

  # Prevent container from being attached to terminal
  attach = false
  logs   = false

  lifecycle {
    ignore_changes = all
  }

  depends_on = [vault_generic_endpoint.service_users]
}

# Frontend service for this environment (if frontend service exists)
resource "docker_container" "frontend_service" {
  count = contains(keys(var.services), "frontend") ? 1 : 0

  image = var.environment_name == "production" ? var.services.frontend.production_image : var.services.frontend.image
  name  = "frontend_${var.environment_name}"

  ports {
    internal = 80
    external = var.environment_config.frontend_port
  }

  networks_advanced {
    name = var.environment_config.network_name
  }

  # Ensure container stays running
  must_run = true
  restart  = "unless-stopped"

  # Prevent container from being attached to terminal
  attach = false
  logs   = false

  lifecycle {
    ignore_changes = all
  }
}
