############ MODULES ############
module "vault_environment_development" {
  source                 = "./modules/vault-environment"
  environment_name       = "development"
  environment_config     = local.environments.development
  services               = local.services
  vault_enabled_services = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }

  providers = {
    vault = vault.development
  }
}

module "vault_environment_staging" {
  source                 = "./modules/vault-environment"
  environment_name       = "staging"
  environment_config     = local.environments.staging
  services               = local.services
  vault_enabled_services = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }

  providers = {
    vault = vault.staging
  }
}

module "vault_environment_production" {
  source                 = "./modules/vault-environment"
  environment_name       = "production"
  environment_config     = local.environments.production
  services               = local.services
  vault_enabled_services = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }

  providers = {
    vault = vault.production
  }
}

terraform {
  required_version = ">= 1.0"

  required_providers {
    vault = {
      source  = "hashicorp/vault"
      version = "3.0.1"
    }
    docker = {
      source  = "kreuzwerker/docker"
      version = "2.15.0"
    }
  }
}
